package com.investment.api.controller

import com.investment.api.model.CreateInstrumentRequest
import com.investment.api.model.InstrumentResponse
import com.investment.api.model.ApiResponse
import com.investment.model.Instrument
import com.investment.model.InstrumentType
import com.investment.service.InstrumentService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import spock.lang.Specification
import com.fasterxml.jackson.databind.ObjectMapper
import java.math.BigDecimal

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*
import static org.mockito.ArgumentMatchers.*
import static org.mockito.Mockito.*

@WebMvcTest(InstrumentController)
class InstrumentControllerCrudSpec extends Specification {

    @Autowired
    MockMvc mockMvc

    @MockBean
    InstrumentService instrumentService

    @Autowired
    ObjectMapper objectMapper

    def "should create instrument successfully"() {
        given: "a valid create instrument request"
        def request = new CreateInstrumentRequest(
            symbol: "TEST",
            name: "Test Company",
            type: InstrumentType.US_STOCK,
            marketCap: new BigDecimal("1000000000"),
            country: "United States",
            ipoYear: 2020,
            sector: "Technology",
            industry: "Software"
        )

        def createdInstrument = new Instrument(
            "TEST", "Test Company", InstrumentType.US_STOCK,
            new BigDecimal("1000000000"), "United States", 2020,
            "Technology", "Software"
        )

        when(instrumentService.createInstrument(any(CreateInstrumentRequest))).thenReturn(createdInstrument)

        when: "creating the instrument"
        def result = mockMvc.perform(post("/api/instruments")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))

        then: "the instrument is created successfully"
        result.andExpect(status().isCreated())
              .andExpect(jsonPath('$.success').value(true))
              .andExpect(jsonPath('$.message').value("Instrument created successfully"))
              .andExpect(jsonPath('$.data.symbol').value("TEST"))
              .andExpect(jsonPath('$.data.name').value("Test Company"))
              .andExpect(jsonPath('$.data.type').value("US_STOCK"))

        verify(instrumentService).createInstrument(any(CreateInstrumentRequest))
    }

    def "should update instrument successfully"() {
        given: "an existing instrument and update request"
        def symbol = "TEST"
        def request = new CreateInstrumentRequest(
            symbol: "TEST",
            name: "Updated Test Company",
            type: InstrumentType.US_STOCK,
            marketCap: new BigDecimal("2000000000"),
            country: "United States",
            ipoYear: 2020,
            sector: "Technology",
            industry: "Software"
        )

        def updatedInstrument = new Instrument(
            "TEST", "Updated Test Company", InstrumentType.US_STOCK,
            new BigDecimal("2000000000"), "United States", 2020,
            "Technology", "Software"
        )

        when(instrumentService.updateInstrument(eq(symbol), any(CreateInstrumentRequest))).thenReturn(updatedInstrument)

        when: "updating the instrument"
        def result = mockMvc.perform(put("/api/instruments/{symbol}", symbol)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))

        then: "the instrument is updated successfully"
        result.andExpect(status().isOk())
              .andExpect(jsonPath('$.success').value(true))
              .andExpect(jsonPath('$.message').value("Instrument updated successfully"))
              .andExpect(jsonPath('$.data.symbol').value("TEST"))
              .andExpect(jsonPath('$.data.name').value("Updated Test Company"))
              .andExpect(jsonPath('$.data.marketCap').value(2000000000))

        verify(instrumentService).updateInstrument(eq(symbol), any(CreateInstrumentRequest))
    }

    def "should delete instrument successfully"() {
        given: "an existing instrument"
        def symbol = "TEST"
        def deletionSummary = "Deleted instrument 'TEST' and associated data: 100 OHLCV records, 2 positions, 1 watchlist items"

        when(instrumentService.deleteInstrument(symbol)).thenReturn(deletionSummary)

        when: "deleting the instrument"
        def result = mockMvc.perform(delete("/api/instruments/{symbol}", symbol))

        then: "the instrument is deleted successfully"
        result.andExpect(status().isOk())
              .andExpect(jsonPath('$.success').value(true))
              .andExpect(jsonPath('$.message').value("Instrument deleted successfully"))
              .andExpect(jsonPath('$.data').value(deletionSummary))

        verify(instrumentService).deleteInstrument(symbol)
    }

    def "should handle update of non-existent instrument"() {
        given: "a non-existent instrument symbol"
        def symbol = "NONEXISTENT"
        def request = new CreateInstrumentRequest(
            symbol: "NONEXISTENT",
            name: "Non-existent Company",
            type: InstrumentType.US_STOCK,
            marketCap: new BigDecimal("1000000000"),
            country: "United States",
            ipoYear: 2020,
            sector: "Technology",
            industry: "Software"
        )

        when(instrumentService.updateInstrument(eq(symbol), any(CreateInstrumentRequest)))
                .thenThrow(new IllegalArgumentException("Instrument not found: " + symbol))

        when: "trying to update the non-existent instrument"
        def result = mockMvc.perform(put("/api/instruments/{symbol}", symbol)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))

        then: "a not found error is returned"
        result.andExpect(status().isNotFound())
              .andExpect(jsonPath('$.success').value(false))
              .andExpect(jsonPath('$.message').value("Instrument not found: " + symbol))

        verify(instrumentService).updateInstrument(eq(symbol), any(CreateInstrumentRequest))
    }

    def "should handle delete of non-existent instrument"() {
        given: "a non-existent instrument symbol"
        def symbol = "NONEXISTENT"

        when(instrumentService.deleteInstrument(symbol))
                .thenThrow(new IllegalArgumentException("Instrument not found: " + symbol))

        when: "trying to delete the non-existent instrument"
        def result = mockMvc.perform(delete("/api/instruments/{symbol}", symbol))

        then: "a not found error is returned"
        result.andExpect(status().isNotFound())
              .andExpect(jsonPath('$.success').value(false))
              .andExpect(jsonPath('$.message').value("Instrument not found: " + symbol))

        verify(instrumentService).deleteInstrument(symbol)
    }

    def "should handle symbol conflict during update"() {
        given: "an update request that conflicts with existing symbol"
        def symbol = "TEST"
        def request = new CreateInstrumentRequest(
            symbol: "EXISTING",
            name: "Test Company",
            type: InstrumentType.US_STOCK,
            marketCap: new BigDecimal("1000000000"),
            country: "United States",
            ipoYear: 2020,
            sector: "Technology",
            industry: "Software"
        )

        when(instrumentService.updateInstrument(eq(symbol), any(CreateInstrumentRequest)))
                .thenThrow(new IllegalArgumentException("New symbol already exists: EXISTING"))

        when: "trying to update with conflicting symbol"
        def result = mockMvc.perform(put("/api/instruments/{symbol}", symbol)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))

        then: "a conflict error is returned"
        result.andExpect(status().isConflict())
              .andExpect(jsonPath('$.success').value(false))
              .andExpect(jsonPath('$.message').value("New symbol already exists: EXISTING"))

        verify(instrumentService).updateInstrument(eq(symbol), any(CreateInstrumentRequest))
    }

    def "should validate required fields during update"() {
        given: "an update request with missing required fields"
        def symbol = "TEST"
        def request = new CreateInstrumentRequest(
            symbol: "",  // Empty symbol
            name: "",    // Empty name
            type: InstrumentType.US_STOCK,
            marketCap: new BigDecimal("1000000000"),
            country: "United States",
            ipoYear: 2020,
            sector: "Technology",
            industry: "Software"
        )

        when: "trying to update with invalid data"
        def result = mockMvc.perform(put("/api/instruments/{symbol}", symbol)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))

        then: "validation errors are returned"
        result.andExpect(status().isBadRequest())

        // Service should not be called due to validation failure
        verify(instrumentService, never()).updateInstrument(any(String), any(CreateInstrumentRequest))
    }
}
