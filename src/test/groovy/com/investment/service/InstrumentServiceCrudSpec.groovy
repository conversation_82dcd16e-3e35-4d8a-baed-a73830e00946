package com.investment.service

import com.investment.api.model.CreateInstrumentRequest
import com.investment.database.DatabaseManager
import com.investment.model.Instrument
import com.investment.model.InstrumentType
import spock.lang.Specification
import java.math.BigDecimal
import java.sql.SQLException

class InstrumentServiceCrudSpec extends Specification {

    InstrumentService instrumentService
    DatabaseManager databaseManager

    def setup() {
        databaseManager = Mock(DatabaseManager)
        instrumentService = new InstrumentService(databaseManager)
    }

    def "should update instrument successfully when symbol doesn't change"() {
        given: "an existing instrument and update request"
        def currentSymbol = "TEST"
        def request = new CreateInstrumentRequest(
            symbol: "TEST",
            name: "Updated Test Company",
            type: InstrumentType.US_STOCK,
            marketCap: new BigDecimal("2000000000"),
            country: "United States",
            ipoYear: 2020,
            sector: "Technology",
            industry: "Software"
        )

        when: "updating the instrument"
        def result = instrumentService.updateInstrument(currentSymbol, request)

        then: "the database operations are called correctly"
        1 * databaseManager.symbolExists(currentSymbol) >> true
        1 * databaseManager.updateInstrument(
            currentSymbol,
            request.symbol,
            request.name,
            request.type.name(),
            request.marketCap,
            request.country,
            request.ipoYear,
            request.sector,
            request.industry
        )

        and: "the updated instrument is returned"
        result.symbol == "TEST"
        result.name == "Updated Test Company"
        result.marketCap == new BigDecimal("2000000000")
    }

    def "should update instrument successfully when symbol changes"() {
        given: "an existing instrument and update request with new symbol"
        def currentSymbol = "TEST"
        def request = new CreateInstrumentRequest(
            symbol: "NEWTEST",
            name: "Updated Test Company",
            type: InstrumentType.US_STOCK,
            marketCap: new BigDecimal("2000000000"),
            country: "United States",
            ipoYear: 2020,
            sector: "Technology",
            industry: "Software"
        )

        when: "updating the instrument"
        def result = instrumentService.updateInstrument(currentSymbol, request)

        then: "the database operations are called correctly"
        1 * databaseManager.symbolExists(currentSymbol) >> true
        1 * databaseManager.symbolExists("NEWTEST") >> false
        1 * databaseManager.updateInstrument(
            currentSymbol,
            "NEWTEST",
            request.name,
            request.type.name(),
            request.marketCap,
            request.country,
            request.ipoYear,
            request.sector,
            request.industry
        )

        and: "the updated instrument is returned with new symbol"
        result.symbol == "NEWTEST"
        result.name == "Updated Test Company"
    }

    def "should throw exception when updating non-existent instrument"() {
        given: "a non-existent instrument symbol"
        def currentSymbol = "NONEXISTENT"
        def request = new CreateInstrumentRequest(
            symbol: "NONEXISTENT",
            name: "Non-existent Company",
            type: InstrumentType.US_STOCK,
            marketCap: new BigDecimal("1000000000"),
            country: "United States",
            ipoYear: 2020,
            sector: "Technology",
            industry: "Software"
        )

        when: "trying to update the non-existent instrument"
        instrumentService.updateInstrument(currentSymbol, request)

        then: "an exception is thrown"
        1 * databaseManager.symbolExists(currentSymbol) >> false
        0 * databaseManager.updateInstrument(_, _, _, _, _, _, _, _, _)
        
        def exception = thrown(IllegalArgumentException)
        exception.message == "Instrument not found: NONEXISTENT"
    }

    def "should throw exception when new symbol already exists"() {
        given: "an update request that conflicts with existing symbol"
        def currentSymbol = "TEST"
        def request = new CreateInstrumentRequest(
            symbol: "EXISTING",
            name: "Test Company",
            type: InstrumentType.US_STOCK,
            marketCap: new BigDecimal("1000000000"),
            country: "United States",
            ipoYear: 2020,
            sector: "Technology",
            industry: "Software"
        )

        when: "trying to update with conflicting symbol"
        instrumentService.updateInstrument(currentSymbol, request)

        then: "an exception is thrown"
        1 * databaseManager.symbolExists(currentSymbol) >> true
        1 * databaseManager.symbolExists("EXISTING") >> true
        0 * databaseManager.updateInstrument(_, _, _, _, _, _, _, _, _)
        
        def exception = thrown(IllegalArgumentException)
        exception.message == "New symbol already exists: EXISTING"
    }

    def "should delete instrument successfully"() {
        given: "an existing instrument"
        def symbol = "TEST"
        def deletionSummary = "Deleted instrument 'TEST' and associated data: 100 OHLCV records, 2 positions, 1 watchlist items"

        when: "deleting the instrument"
        def result = instrumentService.deleteInstrument(symbol)

        then: "the database operations are called correctly"
        1 * databaseManager.symbolExists(symbol) >> true
        1 * databaseManager.deleteInstrumentAndData(symbol) >> deletionSummary

        and: "the deletion summary is returned"
        result == deletionSummary
    }

    def "should throw exception when deleting non-existent instrument"() {
        given: "a non-existent instrument symbol"
        def symbol = "NONEXISTENT"

        when: "trying to delete the non-existent instrument"
        instrumentService.deleteInstrument(symbol)

        then: "an exception is thrown"
        1 * databaseManager.symbolExists(symbol) >> false
        0 * databaseManager.deleteInstrumentAndData(_)
        
        def exception = thrown(IllegalArgumentException)
        exception.message == "Instrument not found: NONEXISTENT"
    }

    def "should validate input parameters for update"() {
        given: "invalid input parameters"
        def currentSymbol = null
        def request = new CreateInstrumentRequest(
            symbol: "TEST",
            name: "Test Company",
            type: InstrumentType.US_STOCK,
            marketCap: new BigDecimal("1000000000"),
            country: "United States",
            ipoYear: 2020,
            sector: "Technology",
            industry: "Software"
        )

        when: "trying to update with null symbol"
        instrumentService.updateInstrument(currentSymbol, request)

        then: "an exception is thrown"
        0 * databaseManager.symbolExists(_)
        0 * databaseManager.updateInstrument(_, _, _, _, _, _, _, _, _)
        
        def exception = thrown(IllegalArgumentException)
        exception.message == "Current symbol cannot be null or empty"
    }

    def "should validate input parameters for delete"() {
        given: "invalid input parameters"
        def symbol = ""

        when: "trying to delete with empty symbol"
        instrumentService.deleteInstrument(symbol)

        then: "an exception is thrown"
        0 * databaseManager.symbolExists(_)
        0 * databaseManager.deleteInstrumentAndData(_)
        
        def exception = thrown(IllegalArgumentException)
        exception.message == "Symbol cannot be null or empty"
    }

    def "should handle database errors during update"() {
        given: "a valid update request but database error"
        def currentSymbol = "TEST"
        def request = new CreateInstrumentRequest(
            symbol: "TEST",
            name: "Updated Test Company",
            type: InstrumentType.US_STOCK,
            marketCap: new BigDecimal("2000000000"),
            country: "United States",
            ipoYear: 2020,
            sector: "Technology",
            industry: "Software"
        )

        when: "updating the instrument with database error"
        instrumentService.updateInstrument(currentSymbol, request)

        then: "the database error is propagated"
        1 * databaseManager.symbolExists(currentSymbol) >> true
        1 * databaseManager.updateInstrument(_, _, _, _, _, _, _, _, _) >> { throw new SQLException("Database error") }
        
        def exception = thrown(SQLException)
        exception.message == "Database error"
    }

    def "should handle database errors during delete"() {
        given: "a valid delete request but database error"
        def symbol = "TEST"

        when: "deleting the instrument with database error"
        instrumentService.deleteInstrument(symbol)

        then: "the database error is propagated"
        1 * databaseManager.symbolExists(symbol) >> true
        1 * databaseManager.deleteInstrumentAndData(symbol) >> { throw new SQLException("Database error") }
        
        def exception = thrown(SQLException)
        exception.message == "Database error"
    }
}
